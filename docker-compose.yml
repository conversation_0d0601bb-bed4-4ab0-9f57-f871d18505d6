services:
  fastapi-ping-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastapi-ping-service
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fastapi-network

networks:
  fastapi-network:
    driver: bridge
