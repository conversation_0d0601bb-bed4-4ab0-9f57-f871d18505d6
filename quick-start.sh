#!/bin/bash

# Quick Start Guide for Docker Registry Push
echo "🚀 FastAPI Docker Registry Push - Quick Start"
echo "============================================="
echo ""

echo "📋 解决 SSL 证书问题的方法："
echo ""

echo "方法 1: 使用 --insecure 参数（最简单）"
echo "  ./push.sh --insecure --tag v1.0.0"
echo ""

echo "方法 2: 配置 Docker 守护进程（推荐）"
echo "  sudo ./configure-insecure-registry.sh"
echo ""

echo "方法 3: 查看详细配置说明"
echo "  cat ssl-fix-guide.md"
echo ""

echo "🎯 常用命令："
echo ""

echo "# 构建镜像"
echo "./build.sh --tag v1.0.0"
echo ""

echo "# 推送镜像（忽略 SSL 错误）"
echo "./push.sh --insecure --tag v1.0.0"
echo ""

echo "# 一键构建并推送（忽略 SSL 错误）"
echo "./push.sh --insecure --login --build --tag v1.0.0"
echo ""

echo "# 配置 Docker 允许不安全的仓库"
echo "sudo ./configure-insecure-registry.sh"
echo ""

echo "📖 更多信息请查看："
echo "  - ./push.sh --help"
echo "  - ./build.sh --help"
echo "  - ./configure-insecure-registry.sh --help"
echo "  - cat ssl-fix-guide.md"
echo ""

echo "✅ 推送成功后，镜像地址为："
echo "   bznreg.bznins.com/bzn/fastapi-ping-service:TAG"
