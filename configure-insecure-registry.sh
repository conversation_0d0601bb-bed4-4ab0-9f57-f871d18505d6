#!/bin/bash

# Configure Docker to allow insecure registry
# This script helps configure <PERSON><PERSON> daemon to skip SSL verification for your private registry

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
REGISTRY="bznreg.bznins.com"
DOCKER_CONFIG_DIR="/etc/docker"
DOCKER_CONFIG_FILE="$DOCKER_CONFIG_DIR/daemon.json"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --show-only)
            SHOW_ONLY="true"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -r, --registry REG    Registry to configure (default: bznreg.bznins.com)"
            echo "  --show-only           Only show configuration, don't modify files"
            echo "  -h, --help            Show this help message"
            echo ""
            echo "This script helps configure Docker to allow insecure registries"
            echo "by adding the registry to Docker daemon's insecure-registries list."
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "🔧 Docker Insecure Registry Configuration"
print_status "Registry: $REGISTRY"
echo ""

# Check if running as root (needed for system-wide Docker config)
if [[ $EUID -ne 0 ]] && [[ "$SHOW_ONLY" != "true" ]]; then
    print_warning "This script needs to be run as root to modify Docker daemon configuration"
    print_status "Run with: sudo $0"
    print_status "Or use: $0 --show-only to see the configuration without applying it"
    echo ""
fi

# Show current Docker configuration
print_status "Current Docker daemon configuration:"
if [[ -f "$DOCKER_CONFIG_FILE" ]]; then
    print_status "Found existing config at: $DOCKER_CONFIG_FILE"
    cat "$DOCKER_CONFIG_FILE" | jq . 2>/dev/null || cat "$DOCKER_CONFIG_FILE"
else
    print_status "No existing Docker daemon config found"
fi
echo ""

# Generate new configuration
print_status "Recommended configuration:"
NEW_CONFIG=$(cat << EOF
{
  "insecure-registries": ["$REGISTRY"],
  "registry-mirrors": []
}
EOF
)

# If existing config, merge with it
if [[ -f "$DOCKER_CONFIG_FILE" ]]; then
    if command -v jq &> /dev/null; then
        # Use jq to merge configurations
        EXISTING_CONFIG=$(cat "$DOCKER_CONFIG_FILE")
        NEW_CONFIG=$(echo "$EXISTING_CONFIG" | jq --arg registry "$REGISTRY" '
            if .["insecure-registries"] then
                .["insecure-registries"] |= (. + [$registry] | unique)
            else
                .["insecure-registries"] = [$registry]
            end
        ')
    else
        print_warning "jq not found, showing basic configuration"
        print_warning "You may need to manually merge with existing config"
    fi
fi

echo "$NEW_CONFIG" | jq . 2>/dev/null || echo "$NEW_CONFIG"
echo ""

if [[ "$SHOW_ONLY" == "true" ]]; then
    print_status "Configuration shown only (--show-only mode)"
    print_status "To apply this configuration:"
    print_status "1. Save the above JSON to $DOCKER_CONFIG_FILE"
    print_status "2. Restart Docker daemon: sudo systemctl restart docker"
    exit 0
fi

# Apply configuration if running as root
if [[ $EUID -eq 0 ]]; then
    print_status "Applying configuration..."
    
    # Create directory if it doesn't exist
    mkdir -p "$DOCKER_CONFIG_DIR"
    
    # Backup existing config
    if [[ -f "$DOCKER_CONFIG_FILE" ]]; then
        cp "$DOCKER_CONFIG_FILE" "$DOCKER_CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        print_status "Backed up existing config"
    fi
    
    # Write new config
    echo "$NEW_CONFIG" > "$DOCKER_CONFIG_FILE"
    print_success "Configuration written to $DOCKER_CONFIG_FILE"
    
    # Restart Docker daemon
    print_status "Restarting Docker daemon..."
    if systemctl restart docker; then
        print_success "Docker daemon restarted successfully"
    else
        print_error "Failed to restart Docker daemon"
        print_error "You may need to restart it manually: sudo systemctl restart docker"
    fi
    
    print_success "Configuration complete!"
else
    print_status "Manual configuration steps:"
    print_status "1. Run as root: sudo $0"
    print_status "2. Or manually create/edit $DOCKER_CONFIG_FILE with the above content"
    print_status "3. Restart Docker: sudo systemctl restart docker"
fi

echo ""
print_status "After configuration, you can push without SSL verification:"
print_status "  ./push.sh --tag v1.0.0"
print_status "  ./push.sh --login --build --tag v1.0.0"
echo ""
print_warning "Note: Using insecure registries reduces security"
print_warning "Consider using proper SSL certificates in production"
