#!/bin/bash

# FastAPI Ping Service - Docker Build Script
# This script builds the Docker image for the FastAPI application

set -e  # Exit on any error

# Configuration
IMAGE_NAME="fastapi-ping-service"
IMAGE_TAG="latest"
DOCKERFILE="Dockerfile"
REGISTRY="bznreg.bznins.com"  # Default registry
NAMESPACE="bzn"  # Default namespace
MULTI_ARCH="true"
PLATFORMS="linux/amd64,linux/arm64"

# Default settings
PUSH_AFTER_BUILD="true"  # Default to push
NO_CACHE=""
ARCH_TAG="false"
PROGRESS=""
QUIET=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        --progress)
            PROGRESS="--progress=plain"
            shift
            ;;
        -q|--quiet)
            QUIET="--quiet"
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --push)
            PUSH_AFTER_BUILD="true"
            shift
            ;;
        --multi-arch)
            MULTI_ARCH="true"
            shift
            ;;
        --single-arch)
            MULTI_ARCH="false"
            PUSH_AFTER_BUILD="false"  # Single arch builds don't auto-push by default
            shift
            ;;
        --platforms)
            PLATFORMS="$2"
            shift 2
            ;;
        --arch-tag)
            ARCH_TAG="true"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -t, --tag TAG         Set image tag (default: latest)"
            echo "  -n, --name NAME       Set image name (default: fastapi-ping-service)"
            echo "  -r, --registry REG    Set registry for tagging (default: bznreg.bznins.com)"
            echo "  --namespace NS        Set namespace for tagging (default: bzn)"
            echo "  --no-cache            Build without using cache"
            echo "  --progress            Show plain build progress"
            echo "  -q, --quiet           Quiet build output"
            echo "  --push                Push to registry after building (default: enabled)"
            echo "  --multi-arch          Build for multiple architectures (default: enabled)"
            echo "  --single-arch         Build for single architecture only"
            echo "  --platforms PLATFORMS Set custom platforms (default: linux/amd64,linux/arm64)"
            echo "  --arch-tag            Add architecture suffix to tag (e.g., latest-amd64, latest-arm64)"
            echo "  -h, --help            Show this help message"
            echo ""
            echo "Default behavior: Multi-arch build and push to bznreg.bznins.com/bzn/"
            echo ""
            echo "Examples:"
            echo "  $0 --tag v1.0.0                      # Multi-arch build and push with tag v1.0.0"
            echo "  $0 --tag v1.0.0 --arch-tag           # Add architecture tags: v1.0.0-amd64, v1.0.0-arm64"
            echo "  $0 --single-arch --tag v1.0.0        # Build only for current platform"
            echo "  $0 --platforms linux/amd64 --tag v1.0.0  # Build only for amd64"
            echo "  $0 --registry my-reg.com --namespace my-ns --tag v1.0.0  # Custom registry"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Check if buildx is available for multi-arch builds
if [[ "$MULTI_ARCH" == "true" ]]; then
    if ! docker buildx version &> /dev/null; then
        print_error "Docker buildx is not available. Multi-arch builds require buildx."
        print_error "Please install Docker Desktop or enable buildx plugin."
        exit 1
    fi

    # Create or use existing buildx builder
    BUILDER_NAME="multiarch-builder"
    if ! docker buildx inspect "$BUILDER_NAME" &> /dev/null; then
        print_status "Creating buildx builder for multi-arch builds..."
        docker buildx create --name "$BUILDER_NAME" --driver docker-container --bootstrap
    fi
    docker buildx use "$BUILDER_NAME"
    print_status "Using buildx builder: $BUILDER_NAME"
fi

# Check if Dockerfile exists
if [[ ! -f "$DOCKERFILE" ]]; then
    print_error "Dockerfile not found: $DOCKERFILE"
    exit 1
fi

# Check if required files exist
print_status "Checking required files..."
required_files=("pyproject.toml" "main.py")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_warning "Required file not found: $file"
    fi
done

# Check Python version compatibility
if [[ -f "pyproject.toml" && -f "Dockerfile" ]]; then
    # Extract Python version from pyproject.toml
    if grep -q "requires-python" pyproject.toml; then
        REQUIRED_PYTHON=$(grep "requires-python" pyproject.toml | sed 's/.*>=\([0-9.]*\).*/\1/')
        # Extract Python version from Dockerfile
        if grep -q "FROM python:" Dockerfile; then
            DOCKER_PYTHON=$(grep "FROM python:" Dockerfile | sed 's/.*python:\([0-9.]*\).*/\1/')
            print_status "Python version check: Dockerfile uses $DOCKER_PYTHON, pyproject.toml requires >=$REQUIRED_PYTHON"

            # Simple version comparison (works for major.minor versions)
            if [[ "$DOCKER_PYTHON" < "$REQUIRED_PYTHON" ]]; then
                print_warning "Python version mismatch detected!"
                print_warning "Dockerfile uses Python $DOCKER_PYTHON but pyproject.toml requires >=$REQUIRED_PYTHON"
                print_warning "This may cause build failures."
            fi
        fi
    fi
fi

# Check available disk space (warn if less than 1GB)
available_space=$(df . | awk 'NR==2 {print $4}')
if [[ $available_space -lt 1048576 ]]; then  # 1GB in KB
    print_warning "Low disk space available. Build might fail."
fi

# Build the Docker image
print_status "Building Docker image: ${IMAGE_NAME}:${IMAGE_TAG}"
print_status "Using Dockerfile: $DOCKERFILE"

if [[ "$MULTI_ARCH" == "true" ]]; then
    print_status "Building for multiple architectures: $PLATFORMS"
fi

if [[ -n "$NO_CACHE" ]]; then
    print_warning "Building without cache"
fi

# Function to generate architecture-specific tags
generate_arch_tags() {
    local base_tag="$1"
    local platforms="$2"
    local registry_prefix="$3"

    # Convert platforms to architecture suffixes
    local arch_tags=""
    IFS=',' read -ra PLATFORM_ARRAY <<< "$platforms"
    for platform in "${PLATFORM_ARRAY[@]}"; do
        # Extract architecture from platform (e.g., linux/amd64 -> amd64)
        local arch=$(echo "$platform" | cut -d'/' -f2)
        local arch_tag="${base_tag}-${arch}"
        if [[ -n "$registry_prefix" ]]; then
            arch_tags="${arch_tags} -t ${registry_prefix}:${arch_tag}"
        else
            arch_tags="${arch_tags} -t ${IMAGE_NAME}:${arch_tag}"
        fi
    done
    echo "$arch_tags"
}

# Determine final image name
if [[ -n "$REGISTRY" && -n "$NAMESPACE" ]]; then
    FULL_IMAGE_NAME="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
    LOCAL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
    REGISTRY_PREFIX="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}"
elif [[ -n "$REGISTRY" ]]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    LOCAL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
    REGISTRY_PREFIX="${REGISTRY}/${IMAGE_NAME}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
    LOCAL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
    REGISTRY_PREFIX=""
fi

# Build command preparation
BUILD_ARGS="${NO_CACHE} ${PROGRESS} ${QUIET}"

if [[ "$MULTI_ARCH" == "true" ]]; then
    # Multi-architecture build using buildx
    print_status "Building multi-arch image: $LOCAL_IMAGE_NAME"
    if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
        print_status "Will also tag as: $FULL_IMAGE_NAME"
    fi
    print_status "Platforms: $PLATFORMS"

    # Prepare buildx command
    BUILDX_CMD="docker buildx build ${BUILD_ARGS} --platform ${PLATFORMS} -f ${DOCKERFILE}"

    # Add main tag
    if [[ "$PUSH_AFTER_BUILD" == "true" && "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
        # Push mode: only add registry tags, not local tags
        BUILDX_CMD="${BUILDX_CMD} -t ${FULL_IMAGE_NAME}"
    else
        # Local mode: add local tag and registry tag if specified
        BUILDX_CMD="${BUILDX_CMD} -t ${LOCAL_IMAGE_NAME}"
        if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            BUILDX_CMD="${BUILDX_CMD} -t ${FULL_IMAGE_NAME}"
        fi
    fi

    # Add architecture-specific tags if requested
    if [[ "$ARCH_TAG" == "true" ]]; then
        ARCH_TAGS=$(generate_arch_tags "$IMAGE_TAG" "$PLATFORMS" "$REGISTRY_PREFIX")
        BUILDX_CMD="${BUILDX_CMD} ${ARCH_TAGS}"
        print_status "Will create architecture-specific tags:"
        IFS=',' read -ra PLATFORM_ARRAY <<< "$PLATFORMS"
        for platform in "${PLATFORM_ARRAY[@]}"; do
            arch=$(echo "$platform" | cut -d'/' -f2)
            if [[ -n "$REGISTRY_PREFIX" ]]; then
                print_status "  - ${REGISTRY_PREFIX}:${IMAGE_TAG}-${arch}"
            else
                print_status "  - ${IMAGE_NAME}:${IMAGE_TAG}-${arch}"
            fi
        done
    fi

    if [[ "$PUSH_AFTER_BUILD" == "true" && "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
        # Push directly during build for multi-arch
        BUILDX_CMD="${BUILDX_CMD} --push"
        print_status "Will push to registry during build"
    else
        # Load to local Docker for single platform testing
        if [[ "$PLATFORMS" == *","* ]]; then
            print_warning "Multi-platform build cannot be loaded to local Docker"
            print_warning "Use --push option to push directly to registry"
        else
            BUILDX_CMD="${BUILDX_CMD} --load"
        fi
    fi

    BUILDX_CMD="${BUILDX_CMD} ."
    print_status "Executing: $BUILDX_CMD"

    # Execute buildx command
    if eval "$BUILDX_CMD"; then
        BUILD_SUCCESS="true"
    else
        BUILD_SUCCESS="false"
    fi
else
    # Traditional single-architecture build
    print_status "Building image: $LOCAL_IMAGE_NAME"
    if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
        print_status "Will also tag as: $FULL_IMAGE_NAME"
    fi
    print_status "Executing: docker build ${BUILD_ARGS} -t ${LOCAL_IMAGE_NAME} -f ${DOCKERFILE} ."

    # Execute the build command with proper error handling
    if docker build ${BUILD_ARGS} -t "${LOCAL_IMAGE_NAME}" -f "${DOCKERFILE}" .; then
        BUILD_SUCCESS="true"
    else
        BUILD_SUCCESS="false"
    fi
fi

# Handle build results
if [[ "$BUILD_SUCCESS" == "true" ]]; then
    if [[ "$MULTI_ARCH" == "true" ]]; then
        print_success "Multi-architecture Docker image built successfully!"
        print_status "Supported platforms: $PLATFORMS"

        if [[ "$PUSH_AFTER_BUILD" == "true" && "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            print_success "Image pushed to registry: $FULL_IMAGE_NAME"
        elif [[ "$PLATFORMS" == *","* ]]; then
            print_warning "Multi-platform images are not available in local Docker"
            print_warning "Use 'docker buildx imagetools inspect $LOCAL_IMAGE_NAME' to verify"
        fi
    else
        print_success "Docker image built successfully: $LOCAL_IMAGE_NAME"

        # Tag for registry if needed (single-arch only)
        if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            print_status "Tagging for registry..."
            if docker tag "$LOCAL_IMAGE_NAME" "$FULL_IMAGE_NAME"; then
                print_success "Tagged as: $FULL_IMAGE_NAME"
            else
                print_warning "Failed to tag for registry"
            fi
        fi

        # Show image info (single-arch only)
        print_status "Image information:"
        if docker images "$LOCAL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}" 2>/dev/null; then
            docker images "$LOCAL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
            if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
                docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}" 2>/dev/null || true
            fi
        else
            # Fallback for older Docker versions
            docker images "$LOCAL_IMAGE_NAME"
        fi
    fi

    # Push if requested (for single-arch builds only, multi-arch already pushed)
    if [[ "$PUSH_AFTER_BUILD" == "true" && "$MULTI_ARCH" != "true" ]]; then
        if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            print_status "Pushing to registry..."
            if docker push "$FULL_IMAGE_NAME"; then
                print_success "Image pushed successfully: $FULL_IMAGE_NAME"
            else
                print_error "Failed to push image"
                exit 1
            fi
        else
            print_warning "Cannot push: no registry specified"
            print_status "Use --registry and --namespace options to enable pushing"
        fi
    fi

    print_status "Build completed successfully!"

    if [[ "$MULTI_ARCH" == "true" ]]; then
        print_status ""
        print_status "Multi-architecture build notes:"
        print_status "- Image supports: $PLATFORMS"
        if [[ "$PLATFORMS" == *","* ]]; then
            print_status "- Multi-platform images are not loaded to local Docker"
            print_status "- Use 'docker buildx imagetools inspect $LOCAL_IMAGE_NAME' to verify"
            print_status "- To test locally, build for single platform: --platforms linux/amd64"
        fi
        if [[ "$PUSH_AFTER_BUILD" != "true" && "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            print_status "- To push multi-arch image: ./build.sh --multi-arch --push --tag $IMAGE_TAG"
        fi
    else
        print_status "To run the container, use: ./run.sh --tag $IMAGE_TAG"
        if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            print_status "To push to registry, use: ./push.sh --tag $IMAGE_TAG"
        fi
    fi

    # Check which compose command is available (for single-arch builds)
    if [[ "$MULTI_ARCH" != "true" ]]; then
        if command -v docker-compose &> /dev/null; then
            print_status "Or use docker-compose: docker-compose up"
        elif docker compose version &> /dev/null 2>&1; then
            print_status "Or use docker compose: docker compose up"
        else
            print_status "Or use the management script: ./docker-manage.sh up"
        fi
    fi
else
    BUILD_EXIT_CODE=$?
    print_error "Failed to build Docker image (exit code: $BUILD_EXIT_CODE)"
    print_error ""
    print_error "Common causes and solutions:"
    print_error "  1. Network connectivity issues:"
    print_error "     - Check internet connection"
    print_error "     - Try using a VPN or different network"
    print_error "     - Configure Docker to use different registry mirrors"
    print_error ""
    print_error "  2. Python version mismatch:"
    print_error "     - Ensure Dockerfile Python version matches pyproject.toml requirements"
    print_error "     - Current Dockerfile: $(grep 'FROM python:' Dockerfile | head -1)"
    print_error "     - Required version: $(grep 'requires-python' pyproject.toml | head -1)"
    print_error ""
    print_error "  3. Missing dependencies:"
    print_error "     - Check if all required files are present"
    print_error "     - Verify uv.lock file is up to date"
    print_error ""
    print_error "  4. System resources:"
    print_error "     - Insufficient disk space"
    print_error "     - Insufficient memory"
    print_error ""
    print_error "  5. Docker issues:"
    print_error "     - Try: docker system prune -f"
    print_error "     - Try: ./build.sh --no-cache"
    if [[ "$MULTI_ARCH" == "true" ]]; then
        print_error ""
        print_error "  6. Multi-arch build issues:"
        print_error "     - Ensure Docker buildx is properly installed"
        print_error "     - Try: docker buildx ls"
        print_error "     - Try: docker buildx create --name multiarch-builder --driver docker-container"
        print_error "     - For registry push issues, ensure you're logged in: docker login"
    fi
    print_error ""
    exit 1
fi
