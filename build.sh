#!/bin/bash

# FastAPI Ping Service - Docker Build Script
# This script builds the Docker image for the FastAPI application

set -e  # Exit on any error

# Configuration
IMAGE_NAME="fastapi-ping-service"
IMAGE_TAG="latest"
DOCKERFILE="Dockerfile"
REGISTRY=""
NAMESPACE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        --progress)
            PROGRESS="--progress=plain"
            shift
            ;;
        -q|--quiet)
            QUIET="--quiet"
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --push)
            PUSH_AFTER_BUILD="true"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -t, --tag TAG         Set image tag (default: latest)"
            echo "  -n, --name NAME       Set image name (default: fastapi-ping-service)"
            echo "  -r, --registry REG    Set registry for tagging (e.g., bznreg.bznins.com)"
            echo "  --namespace NS        Set namespace for tagging (e.g., bzn)"
            echo "  --no-cache            Build without using cache"
            echo "  --progress            Show plain build progress"
            echo "  -q, --quiet           Quiet build output"
            echo "  --push                Push to registry after building"
            echo "  -h, --help            Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Check if Dockerfile exists
if [[ ! -f "$DOCKERFILE" ]]; then
    print_error "Dockerfile not found: $DOCKERFILE"
    exit 1
fi

# Check if required files exist
print_status "Checking required files..."
required_files=("pyproject.toml" "main.py")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_warning "Required file not found: $file"
    fi
done

# Check Python version compatibility
if [[ -f "pyproject.toml" && -f "Dockerfile" ]]; then
    # Extract Python version from pyproject.toml
    if grep -q "requires-python" pyproject.toml; then
        REQUIRED_PYTHON=$(grep "requires-python" pyproject.toml | sed 's/.*>=\([0-9.]*\).*/\1/')
        # Extract Python version from Dockerfile
        if grep -q "FROM python:" Dockerfile; then
            DOCKER_PYTHON=$(grep "FROM python:" Dockerfile | sed 's/.*python:\([0-9.]*\).*/\1/')
            print_status "Python version check: Dockerfile uses $DOCKER_PYTHON, pyproject.toml requires >=$REQUIRED_PYTHON"

            # Simple version comparison (works for major.minor versions)
            if [[ "$DOCKER_PYTHON" < "$REQUIRED_PYTHON" ]]; then
                print_warning "Python version mismatch detected!"
                print_warning "Dockerfile uses Python $DOCKER_PYTHON but pyproject.toml requires >=$REQUIRED_PYTHON"
                print_warning "This may cause build failures."
            fi
        fi
    fi
fi

# Check available disk space (warn if less than 1GB)
available_space=$(df . | awk 'NR==2 {print $4}')
if [[ $available_space -lt 1048576 ]]; then  # 1GB in KB
    print_warning "Low disk space available. Build might fail."
fi

# Build the Docker image
print_status "Building Docker image: ${IMAGE_NAME}:${IMAGE_TAG}"
print_status "Using Dockerfile: $DOCKERFILE"

if [[ -n "$NO_CACHE" ]]; then
    print_warning "Building without cache"
fi

# Determine final image name
if [[ -n "$REGISTRY" && -n "$NAMESPACE" ]]; then
    FULL_IMAGE_NAME="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
    LOCAL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
elif [[ -n "$REGISTRY" ]]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    LOCAL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
    LOCAL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

# Build command
BUILD_ARGS="${NO_CACHE} ${PROGRESS} ${QUIET}"
print_status "Building image: $LOCAL_IMAGE_NAME"
if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
    print_status "Will also tag as: $FULL_IMAGE_NAME"
fi
print_status "Executing: docker build ${BUILD_ARGS} -t ${LOCAL_IMAGE_NAME} -f ${DOCKERFILE} ."

# Execute the build command with proper error handling
if docker build ${BUILD_ARGS} -t "${LOCAL_IMAGE_NAME}" -f "${DOCKERFILE}" .; then
    print_success "Docker image built successfully: $LOCAL_IMAGE_NAME"

    # Tag for registry if needed
    if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
        print_status "Tagging for registry..."
        if docker tag "$LOCAL_IMAGE_NAME" "$FULL_IMAGE_NAME"; then
            print_success "Tagged as: $FULL_IMAGE_NAME"
        else
            print_warning "Failed to tag for registry"
        fi
    fi

    # Show image info
    print_status "Image information:"
    if docker images "$LOCAL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}" 2>/dev/null; then
        docker images "$LOCAL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
        if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}" 2>/dev/null || true
        fi
    else
        # Fallback for older Docker versions
        docker images "$LOCAL_IMAGE_NAME"
    fi

    # Push if requested
    if [[ "$PUSH_AFTER_BUILD" == "true" ]]; then
        if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
            print_status "Pushing to registry..."
            if docker push "$FULL_IMAGE_NAME"; then
                print_success "Image pushed successfully: $FULL_IMAGE_NAME"
            else
                print_error "Failed to push image"
                exit 1
            fi
        else
            print_warning "Cannot push: no registry specified"
            print_status "Use --registry and --namespace options to enable pushing"
        fi
    fi

    print_status "Build completed successfully!"
    print_status "To run the container, use: ./run.sh --tag $IMAGE_TAG"

    if [[ "$FULL_IMAGE_NAME" != "$LOCAL_IMAGE_NAME" ]]; then
        print_status "To push to registry, use: ./push.sh --tag $IMAGE_TAG"
    fi

    # Check which compose command is available
    if command -v docker-compose &> /dev/null; then
        print_status "Or use docker-compose: docker-compose up"
    elif docker compose version &> /dev/null 2>&1; then
        print_status "Or use docker compose: docker compose up"
    else
        print_status "Or use the management script: ./docker-manage.sh up"
    fi
else
    BUILD_EXIT_CODE=$?
    print_error "Failed to build Docker image (exit code: $BUILD_EXIT_CODE)"
    print_error ""
    print_error "Common causes and solutions:"
    print_error "  1. Network connectivity issues:"
    print_error "     - Check internet connection"
    print_error "     - Try using a VPN or different network"
    print_error "     - Configure Docker to use different registry mirrors"
    print_error ""
    print_error "  2. Python version mismatch:"
    print_error "     - Ensure Dockerfile Python version matches pyproject.toml requirements"
    print_error "     - Current Dockerfile: $(grep 'FROM python:' Dockerfile | head -1)"
    print_error "     - Required version: $(grep 'requires-python' pyproject.toml | head -1)"
    print_error ""
    print_error "  3. Missing dependencies:"
    print_error "     - Check if all required files are present"
    print_error "     - Verify uv.lock file is up to date"
    print_error ""
    print_error "  4. System resources:"
    print_error "     - Insufficient disk space"
    print_error "     - Insufficient memory"
    print_error ""
    print_error "  5. Docker issues:"
    print_error "     - Try: docker system prune -f"
    print_error "     - Try: ./build.sh --no-cache"
    print_error ""
    exit 1
fi
