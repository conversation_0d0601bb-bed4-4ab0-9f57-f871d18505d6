# Docker Demo Project

> Docker 镜像打包、推送验证项目 - 基于 FastAPI 的完整 Docker 工作流演示

[![Python Version](https://img.shields.io/badge/Python-3.12+-3776AB?style=flat-square&logo=python)](https://python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-009688?style=flat-square&logo=fastapi)](https://fastapi.tiangolo.com/)
[![Docker](https://img.shields.io/badge/Docker-20.10+-2496ED?style=flat-square&logo=docker)](https://docker.com/)

## 📋 项目简介

这是一个用于验证 Docker 镜像打包、推送等操作的演示项目。项目包含一个简单的 FastAPI 应用程序，以及完整的 Docker 构建、推送和管理脚本，旨在帮助开发者学习和验证 Docker 容器化工作流程。

## ✨ 功能特性

- 🐍 **FastAPI 应用** - 简单的 Python Web 服务，包含健康检查端点
- 🐳 **多阶段构建** - 优化的 Dockerfile，使用多阶段构建减小镜像体积
- 🚀 **自动化脚本** - 完整的构建、推送、运行脚本
- 🔧 **Docker Compose** - 支持 Docker Compose 编排
- 🛡️ **安全配置** - 非 root 用户运行，健康检查配置
- 📊 **详细日志** - 彩色输出和详细的操作日志
- 🌐 **私有仓库支持** - 支持推送到私有 Docker 仓库

## 🚀 快速开始

### 前置要求

- Docker 20.10+
- Python 3.12+ (仅用于本地开发)
- Git

### 方式一：使用 Docker Compose（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd docker-demo

# 使用 Docker Compose 启动
docker compose up --build

# 或使用传统命令
docker-compose up --build
```

### 方式二：使用自动化脚本

```bash
# 1. 构建镜像
./build.sh

# 2. 运行容器
./run.sh

# 3. 推送到私有仓库（可选）
./push.sh --registry your-registry.com --namespace your-namespace
```

### 方式三：手动 Docker 命令

```bash
# 构建镜像
docker build -t fastapi-ping-service:latest .

# 运行容器
docker run -d -p 8000:8000 --name fastapi-service fastapi-ping-service:latest

# 查看日志
docker logs fastapi-service
```

## 📖 使用指南

### 应用程序端点

启动后，应用程序将在 `http://localhost:8000` 提供以下端点：

- `GET /` - 根端点，返回服务基本信息
- `GET /ping` - 健康检查端点，返回 "pong" 响应
- `GET /docs` - FastAPI 自动生成的 API 文档 (Swagger UI)
- `GET /redoc` - 替代的 API 文档界面

### 脚本使用说明

#### 1. 构建脚本 (build.sh)

```bash
# 基本构建
./build.sh

# 指定标签和名称
./build.sh --tag v1.0.0 --name my-app

# 无缓存构建
./build.sh --no-cache

# 构建并推送到仓库
./build.sh --registry your-registry.com --namespace your-ns --push

# 查看帮助
./build.sh --help
```

#### 2. 推送脚本 (push.sh)

```bash
# 基本推送
./push.sh

# 指定标签推送
./push.sh --tag v1.0.0

# 登录并推送
./push.sh --login --tag v1.0.0

# 推送到不安全的仓库
./push.sh --insecure --tag v1.0.0

# 构建并推送
./push.sh --build --login --tag v1.0.0
```

#### 3. 运行脚本 (run.sh)

```bash
# 基本运行
./run.sh

# 指定标签运行
./run.sh --tag v1.0.0

# 后台运行
./run.sh --detach

# 查看帮助
./run.sh --help
```

## 📁 项目结构

```
docker-demo/
├── main.py                     # FastAPI 应用程序主文件
├── Dockerfile                  # Docker 镜像构建文件
├── docker-compose.yml          # Docker Compose 配置
├── pyproject.toml             # Python 项目配置和依赖
├── uv.lock                    # 依赖锁定文件
├── build.sh                   # Docker 镜像构建脚本
├── push.sh                    # Docker 镜像推送脚本
├── run.sh                     # Docker 容器运行脚本
├── docker-manage.sh           # Docker 管理脚本
├── quick-start.sh             # 快速启动脚本
├── configure-insecure-registry.sh  # 不安全仓库配置脚本
├── ssl-fix-guide.md           # SSL 问题修复指南
├── CHANGELOG.md               # 变更日志
└── README.md                  # 项目文档
```

### 核心文件说明

- **main.py**: 基于 FastAPI 的简单 Web 服务，提供健康检查和基本信息端点
- **Dockerfile**: 多阶段构建配置，优化镜像大小和安全性
- **docker-compose.yml**: 容器编排配置，包含网络和健康检查设置
- **build.sh**: 功能完整的构建脚本，支持多种构建选项和错误处理
- **push.sh**: 镜像推送脚本，支持私有仓库和不安全连接
- **run.sh**: 容器运行脚本，提供灵活的运行选项

## 🔧 开发相关

### 开发环境要求

- Python 3.12+
- Docker 20.10+
- Git
- uv (Python 包管理器，可选)

### 主要依赖

- [FastAPI](https://fastapi.tiangolo.com/) - 现代 Python Web 框架
- [Uvicorn](https://www.uvicorn.org/) - ASGI 服务器
- [uv](https://github.com/astral-sh/uv) - 快速 Python 包管理器

### 本地开发

```bash
# 克隆项目
git clone <repository-url>
cd docker-demo

# 使用 uv 安装依赖（推荐）
uv sync

# 或使用 pip
pip install -r requirements.txt

# 本地运行应用
python main.py
# 或
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 访问应用
curl http://localhost:8000/ping
```

### Docker 开发工作流

```bash
# 1. 构建开发镜像
./build.sh --tag dev

# 2. 运行开发容器
./run.sh --tag dev --name dev-container

# 3. 查看容器日志
docker logs -f dev-container

# 4. 进入容器调试
docker exec -it dev-container bash
```

## 🐳 Docker 最佳实践

本项目演示了以下 Docker 最佳实践：

### 1. 多阶段构建
- 使用构建阶段安装依赖，运行阶段只包含必要文件
- 减小最终镜像体积，提高安全性

### 2. 安全配置
- 使用非 root 用户运行应用
- 最小化系统依赖
- 定期清理包管理器缓存

### 3. 健康检查
- 配置容器健康检查
- 支持容器编排系统的健康监控

### 4. 环境变量管理
- 合理使用环境变量配置应用
- 支持不同环境的配置

## 🔍 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理 Docker 缓存
   docker system prune -f

   # 无缓存构建
   ./build.sh --no-cache
   ```

2. **推送失败**
   ```bash
   # 检查登录状态
   docker login your-registry.com

   # 使用不安全连接
   ./push.sh --insecure
   ```

3. **容器无法启动**
   ```bash
   # 查看详细日志
   docker logs container-name

   # 检查端口占用
   netstat -tulpn | grep 8000
   ```

## 📚 学习资源

- [Docker 官方文档](https://docs.docker.com/)
- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [Python Docker 最佳实践](https://docs.docker.com/language/python/)
- [多阶段构建指南](https://docs.docker.com/build/building/multi-stage/)

---

<div align="center">
  <p>🎯 这是一个 Docker 学习和验证项目</p>
  <p>📖 通过实践学习 Docker 容器化最佳实践</p>
</div>