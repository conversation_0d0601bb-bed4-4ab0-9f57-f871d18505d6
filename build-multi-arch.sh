#!/bin/bash

# Multi-Architecture Build Script for FastAPI Ping Service
# This script demonstrates how to build Docker images for multiple architectures

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🐳 Multi-Architecture Docker Build Demo"
echo "======================================="
echo ""

# Check if buildx is available
if ! docker buildx version &> /dev/null; then
    print_error "Docker buildx is not available!"
    print_error "Please install Docker Desktop or enable buildx plugin."
    exit 1
fi

print_status "Docker buildx is available ✓"
echo ""

# Show available builders
print_status "Available buildx builders:"
docker buildx ls
echo ""

# Example 1: Build for current platform only
print_status "Example 1: Building for current platform ($(uname -m))"
echo "Command: ./build.sh --tag demo-single"
echo ""
read -p "Press Enter to continue or Ctrl+C to skip..."
./build.sh --tag demo-single
echo ""

# Example 2: Build for multiple architectures (local testing)
print_status "Example 2: Building for multiple architectures (amd64 + arm64)"
print_warning "Note: Multi-platform images cannot be loaded to local Docker"
echo "Command: ./build.sh --multi-arch --tag demo-multi"
echo ""
read -p "Press Enter to continue or Ctrl+C to skip..."
./build.sh --multi-arch --tag demo-multi
echo ""

# Example 3: Build for specific platform
print_status "Example 3: Building for x86_64/amd64 only (for server deployment)"
echo "Command: ./build.sh --multi-arch --platforms linux/amd64 --tag demo-amd64"
echo ""
read -p "Press Enter to continue or Ctrl+C to skip..."
./build.sh --multi-arch --platforms linux/amd64 --tag demo-amd64
echo ""

# Example 4: Build and push to registry (if configured)
if [[ -n "${DOCKER_REGISTRY:-}" && -n "${DOCKER_NAMESPACE:-}" ]]; then
    print_status "Example 4: Building multi-arch and pushing to registry"
    echo "Registry: $DOCKER_REGISTRY"
    echo "Namespace: $DOCKER_NAMESPACE"
    echo "Command: ./build.sh --multi-arch --push --registry $DOCKER_REGISTRY --namespace $DOCKER_NAMESPACE --tag demo-pushed"
    echo ""
    read -p "Press Enter to continue or Ctrl+C to skip..."
    ./build.sh --multi-arch --push --registry "$DOCKER_REGISTRY" --namespace "$DOCKER_NAMESPACE" --tag demo-pushed
else
    print_status "Example 4: Build and push (skipped - no registry configured)"
    print_status "To enable, set environment variables:"
    print_status "  export DOCKER_REGISTRY=your-registry.com"
    print_status "  export DOCKER_NAMESPACE=your-namespace"
    echo ""
fi

print_success "Multi-architecture build demo completed!"
echo ""
print_status "Summary of commands demonstrated:"
print_status "  ./build.sh --tag demo-single                    # Single platform"
print_status "  ./build.sh --multi-arch --tag demo-multi        # Multi platform"
print_status "  ./build.sh --multi-arch --platforms linux/amd64 # Specific platform"
print_status "  ./build.sh --multi-arch --push                  # Build and push"
echo ""
print_status "For production use:"
print_status "  1. Build multi-arch for compatibility: ./build.sh --multi-arch"
print_status "  2. Push to registry: ./push.sh --multi-arch"
print_status "  3. Or combine: ./build.sh --multi-arch --push --registry your-registry.com"
