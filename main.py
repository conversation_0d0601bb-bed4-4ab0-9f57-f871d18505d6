from fastapi import FastAPI
from typing import Dict

# Create FastAPI application instance
app = FastAPI(
    title="FastAPI Ping Service",
    description="A simple FastAPI service with a health check endpoint",
    version="0.1.0"
)


@app.get("/ping", response_model=Dict[str, str])
async def ping() -> Dict[str, str]:
    """
    Health check endpoint that returns a simple pong response.

    Returns:
        Dict[str, str]: A dictionary containing status and message
    """
    return {"status": "ok", "message": "pong"}


@app.get("/")
async def root() -> Dict[str, str]:
    """
    Root endpoint that provides basic information about the service.

    Returns:
        Dict[str, str]: A dictionary with service information
    """
    return {
        "service": "FastAPI Ping Service",
        "version": "0.1.0",
        "status": "running"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
