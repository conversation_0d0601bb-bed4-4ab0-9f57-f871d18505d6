# 多架构 Docker 构建指南

## 问题背景

在 macOS ARM (Apple Silicon) 上构建的 Docker 镜像默认是 ARM64 架构，无法在 x86_64 服务器上运行。这会导致以下错误：

```
exec /usr/local/bin/python: exec format error
```

或者：

```
WARNING: The requested image's platform (linux/arm64) does not match the detected host platform (linux/amd64)
```

## 解决方案

使用 Docker buildx 进行多架构构建，同时支持 ARM64 和 x86_64 架构。

## 快速开始

### 1. 检查 buildx 支持

```bash
# 检查 buildx 是否可用
docker buildx version

# 查看当前构建器
docker buildx ls
```

### 2. 基本多架构构建

```bash
# 构建支持 ARM64 和 x86_64 的镜像
./build.sh --multi-arch

# 只构建 x86_64 架构（用于服务器部署）
./build.sh --multi-arch --platforms linux/amd64

# 只构建 ARM64 架构（用于 Apple Silicon）
./build.sh --multi-arch --platforms linux/arm64
```

### 3. 构建并推送到仓库

```bash
# 多架构构建并推送
./build.sh --multi-arch --push --registry your-registry.com --namespace your-namespace

# 或者分步操作
./build.sh --multi-arch
./push.sh --multi-arch
```

## 详细使用场景

### 场景 1: 本地开发（Apple Silicon Mac）

```bash
# 为本地测试构建 ARM64 镜像
./build.sh --tag dev-local

# 为服务器部署构建 x86_64 镜像
./build.sh --multi-arch --platforms linux/amd64 --tag prod-amd64
```

### 场景 2: CI/CD 流水线

```bash
# 构建多架构镜像并推送到仓库
./build.sh --multi-arch --push --registry registry.example.com --namespace myapp --tag v1.0.0
```

### 场景 3: 兼容性最大化

```bash
# 构建支持所有主流架构的镜像
./build.sh --multi-arch --platforms linux/amd64,linux/arm64,linux/arm/v7 --tag universal
```

## 重要注意事项

### 1. 多平台镜像限制

- 多平台镜像无法加载到本地 Docker
- 需要推送到仓库才能使用
- 本地测试时建议构建单一平台

### 2. 构建时间

- 多架构构建时间较长
- 建议在 CI/CD 中使用缓存

### 3. 仓库要求

- 需要支持多架构的 Docker 仓库
- 推送前确保已登录仓库

## 故障排除

### 问题 1: buildx 不可用

```bash
# 安装 buildx（如果使用旧版 Docker）
docker buildx install

# 或升级到 Docker Desktop 最新版
```

### 问题 2: 构建器创建失败

```bash
# 手动创建构建器
docker buildx create --name multiarch-builder --driver docker-container --bootstrap
docker buildx use multiarch-builder
```

### 问题 3: 推送失败

```bash
# 确保已登录仓库
docker login your-registry.com

# 检查网络连接
docker buildx imagetools inspect your-registry.com/namespace/image:tag
```

## 验证多架构镜像

```bash
# 检查镜像支持的架构
docker buildx imagetools inspect your-image:tag

# 输出示例：
# Name:      your-image:tag
# MediaType: application/vnd.docker.distribution.manifest.list.v2+json
# Digest:    sha256:...
# 
# Manifests:
#   Name:      your-image:tag@sha256:...
#   MediaType: application/vnd.docker.distribution.manifest.v2+json
#   Platform:  linux/amd64
#   
#   Name:      your-image:tag@sha256:...
#   MediaType: application/vnd.docker.distribution.manifest.v2+json
#   Platform:  linux/arm64
```

## 最佳实践

1. **开发阶段**: 使用单架构构建提高速度
2. **测试阶段**: 在目标架构上测试镜像
3. **生产部署**: 使用多架构镜像确保兼容性
4. **CI/CD**: 自动化多架构构建和推送

## 示例工作流

```bash
# 1. 开发阶段 - 快速本地构建
./build.sh --tag dev

# 2. 测试阶段 - 构建目标架构
./build.sh --multi-arch --platforms linux/amd64 --tag test

# 3. 生产部署 - 多架构构建并推送
./build.sh --multi-arch --push --registry prod-registry.com --namespace myapp --tag v1.0.0
```
