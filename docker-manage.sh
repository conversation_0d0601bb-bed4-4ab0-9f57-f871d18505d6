#!/bin/bash

# FastAPI Ping Service - Docker Compose Management Script
# This script provides easy management of the Docker Compose setup

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "FastAPI Ping Service - Docker Compose Management"
    echo ""
    echo "Usage: $0 COMMAND [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  up              Start services"
    echo "  down            Stop services"
    echo "  restart         Restart services"
    echo "  build           Build images"
    echo "  logs            Show logs"
    echo "  status          Show service status"
    echo "  clean           Clean up containers and images"
    echo "  test            Test the running application"
    echo "  health          Check service health"
    echo ""
    echo "Options:"
    echo "  -d, --detach    Run in background (for up command)"
    echo "  -f, --follow    Follow logs (for logs command)"
    echo "  --build         Force rebuild (for up command)"
    echo "  -h, --help      Show this help message"
}

# Check if docker-compose is available
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE="docker-compose"
        print_status "Using docker-compose (legacy)"
    elif docker compose version &> /dev/null 2>&1; then
        DOCKER_COMPOSE="docker compose"
        print_status "Using docker compose (modern)"
    else
        print_error "Docker Compose is not installed or not available"
        print_error "Please install Docker Compose:"
        print_error "  - For legacy: pip install docker-compose"
        print_error "  - For modern: Install Docker Desktop or Docker Engine with Compose plugin"
        exit 1
    fi
}

# Check if docker-compose.yml exists
check_compose_file() {
    if [[ ! -f "docker-compose.yml" ]]; then
        print_error "docker-compose.yml not found in current directory"
        print_error "Please run this script from the project root directory"
        exit 1
    fi
}

# Execute docker compose command with error handling
execute_compose() {
    local full_command="$DOCKER_COMPOSE $*"

    print_status "Executing: $full_command"

    # Use eval to properly handle complex commands
    if eval "$full_command"; then
        return 0
    else
        local exit_code=$?
        print_error "Docker Compose command failed with exit code: $exit_code"
        print_error "Command: $full_command"
        return $exit_code
    fi
}

# Main command handling
case "${1:-}" in
    up)
        check_docker_compose
        check_compose_file
        shift
        DETACH=""
        BUILD=""

        while [[ $# -gt 0 ]]; do
            case $1 in
                -d|--detach)
                    DETACH="-d"
                    shift
                    ;;
                --build)
                    BUILD="--build"
                    shift
                    ;;
                *)
                    print_error "Unknown option for up command: $1"
                    exit 1
                    ;;
            esac
        done

        print_status "Starting FastAPI Ping Service..."
        if execute_compose "up $DETACH $BUILD"; then
            if [[ -n "$DETACH" ]]; then
                print_success "Services started in background"
                print_status "Application available at: http://localhost:8000"
                print_status "Use '$0 logs' to view logs"
                print_status "Use '$0 down' to stop services"
            fi
        else
            print_error "Failed to start services"
            exit 1
        fi
        ;;

    down)
        check_docker_compose
        check_compose_file
        print_status "Stopping FastAPI Ping Service..."
        if execute_compose "down"; then
            print_success "Services stopped"
        else
            print_error "Failed to stop services"
            exit 1
        fi
        ;;

    restart)
        check_docker_compose
        check_compose_file
        print_status "Restarting FastAPI Ping Service..."
        if execute_compose "restart"; then
            print_success "Services restarted"
        else
            print_error "Failed to restart services"
            exit 1
        fi
        ;;

    build)
        check_docker_compose
        check_compose_file
        print_status "Building FastAPI Ping Service images..."
        if execute_compose "build"; then
            print_success "Images built successfully"
        else
            print_error "Failed to build images"
            exit 1
        fi
        ;;
        
    logs)
        check_docker_compose
        check_compose_file
        shift
        FOLLOW=""
        SERVICE=""

        while [[ $# -gt 0 ]]; do
            case $1 in
                -f|--follow)
                    FOLLOW="-f"
                    shift
                    ;;
                *)
                    # Assume it's a service name
                    SERVICE="$1"
                    shift
                    ;;
            esac
        done

        if execute_compose "logs $FOLLOW $SERVICE"; then
            # Logs command doesn't need success message
            :
        else
            print_error "Failed to retrieve logs"
            exit 1
        fi
        ;;

    status)
        check_docker_compose
        check_compose_file
        print_status "Service status:"
        if execute_compose "ps"; then
            # Status shown by ps command
            :
        else
            print_error "Failed to get service status"
            exit 1
        fi
        ;;

    clean)
        check_docker_compose
        check_compose_file
        print_warning "This will remove all containers and images for this project"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Cleaning up..."
            if execute_compose "down --rmi all --volumes --remove-orphans"; then
                print_success "Cleanup completed"
            else
                print_error "Cleanup failed"
                exit 1
            fi
        else
            print_status "Cleanup cancelled"
        fi
        ;;

    test)
        check_docker_compose
        check_compose_file
        print_status "Testing FastAPI Ping Service..."

        # Check if services are running
        if ! execute_compose "ps --services --filter status=running" | grep -q "fastapi-ping-service"; then
            print_error "FastAPI service is not running"
            print_status "Start the service first with: $0 up -d"
            exit 1
        fi

        # Test ping endpoint
        print_status "Testing /ping endpoint..."
        if curl -s -f http://localhost:8000/ping > /dev/null; then
            print_success "✓ /ping endpoint is working"
        else
            print_error "✗ /ping endpoint failed"
            exit 1
        fi

        # Test root endpoint
        print_status "Testing root endpoint..."
        if curl -s -f http://localhost:8000/ > /dev/null; then
            print_success "✓ Root endpoint is working"
        else
            print_error "✗ Root endpoint failed"
            exit 1
        fi

        print_success "All tests passed! 🎉"
        ;;

    health)
        check_docker_compose
        check_compose_file
        print_status "Checking service health..."

        # Get container health status
        CONTAINER_NAME="fastapi-ping-service"
        if docker ps --format "{{.Names}}" | grep -q "$CONTAINER_NAME"; then
            HEALTH_STATUS=$(docker inspect "$CONTAINER_NAME" --format='{{.State.Health.Status}}' 2>/dev/null || echo "no-healthcheck")
            STATUS=$(docker ps --format "{{.Status}}" --filter "name=$CONTAINER_NAME")

            print_status "Container: $CONTAINER_NAME"
            print_status "Status: $STATUS"
            print_status "Health: $HEALTH_STATUS"

            if [[ "$HEALTH_STATUS" == "healthy" ]]; then
                print_success "✓ Service is healthy"
            elif [[ "$HEALTH_STATUS" == "unhealthy" ]]; then
                print_error "✗ Service is unhealthy"
                exit 1
            else
                print_status "ℹ Health check status: $HEALTH_STATUS"
            fi
        else
            print_error "Container $CONTAINER_NAME is not running"
            exit 1
        fi
        ;;

    -h|--help|help)
        show_usage
        ;;
        
    "")
        print_error "No command specified"
        show_usage
        exit 1
        ;;
        
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
