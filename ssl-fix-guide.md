# 🔒 解决 Docker 私有仓库 SSL 证书问题

## 问题描述
推送到私有仓库 `bznreg.bznins.com` 时遇到证书错误：
```
tls: failed to verify certificate: x509: certificate signed by unknown authority
```

## 🚀 快速解决方案

### 方案 1: 使用 --insecure 参数（最简单）
```bash
# 推送时忽略 SSL 证书验证
./push.sh --insecure --tag v1.0.0

# 登录、构建并推送（忽略证书）
./push.sh --insecure --login --build --tag v1.0.0
```

### 方案 2: 配置 Docker 守护进程（推荐）
```bash
# 查看推荐配置
./configure-insecure-registry.sh --show-only

# 自动配置（需要 root 权限）
sudo ./configure-insecure-registry.sh

# 手动配置
sudo nano /etc/docker/daemon.json
```

添加以下内容到 `/etc/docker/daemon.json`：
```json
{
  "insecure-registries": ["bznreg.bznins.com"]
}
```

然后重启 Docker：
```bash
sudo systemctl restart docker
```

### 方案 3: 临时环境变量（Docker Desktop）
```bash
# 对于 Docker Desktop，可以在设置中添加不安全的仓库
# 或者使用环境变量
export DOCKER_CONTENT_TRUST=0
./push.sh --tag v1.0.0
```

## 📋 完整工作流程

### 一次性设置（选择其一）：

**选项 A: 自动配置**
```bash
sudo ./configure-insecure-registry.sh
```

**选项 B: 手动配置**
```bash
# 1. 编辑 Docker 配置
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<EOF
{
  "insecure-registries": ["bznreg.bznins.com"]
}
EOF

# 2. 重启 Docker
sudo systemctl restart docker
```

### 日常使用：
```bash
# 登录到仓库
docker login bznreg.bznins.com

# 构建并推送
./push.sh --build --tag v1.0.0

# 或者一键完成
./push.sh --login --build --tag v1.0.0
```

## 🔧 可用的推送选项

```bash
# 基本推送
./push.sh --tag v1.0.0

# 忽略 SSL 证书
./push.sh --insecure --tag v1.0.0

# 登录并推送
./push.sh --login --tag v1.0.0

# 构建并推送
./push.sh --build --tag v1.0.0

# 完整流程（登录、构建、推送）
./push.sh --login --build --tag v1.0.0

# 忽略证书的完整流程
./push.sh --insecure --login --build --tag v1.0.0
```

## ⚠️ 安全注意事项

1. **生产环境建议**：
   - 使用有效的 SSL 证书
   - 避免使用 `insecure-registries`

2. **开发环境**：
   - 可以安全使用 `insecure-registries`
   - 确保仓库在受信任的网络中

3. **最佳实践**：
   - 定期更新证书
   - 使用强密码和双因素认证
   - 限制仓库访问权限

## 🔍 故障排除

### 如果仍然无法推送：
```bash
# 检查 Docker 配置
docker info | grep -i insecure

# 检查网络连接
ping bznreg.bznins.com

# 查看详细错误
docker push bznreg.bznins.com/bzn/fastapi-ping-service:v1.0.0 -v

# 重启 Docker 服务
sudo systemctl restart docker
```

### 验证配置：
```bash
# 测试登录
docker login bznreg.bznins.com

# 测试推送
./push.sh --tag test
```

现在您应该能够成功推送镜像到私有仓库了！🎉
