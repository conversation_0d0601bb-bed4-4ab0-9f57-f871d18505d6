#!/bin/bash

# FastAPI Ping Service - Docker Run Script
# This script runs the Docker container for the FastAPI application

set -e  # Exit on any error

# Configuration
IMAGE_NAME="fastapi-ping-service"
IMAGE_TAG="latest"
CONTAINER_NAME="fastapi-ping-service"
HOST_PORT="8000"
CONTAINER_PORT="8000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            HOST_PORT="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -d|--detach)
            DETACH="-d"
            shift
            ;;
        --rm)
            REMOVE="--rm"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -p, --port PORT   Set host port (default: 8000)"
            echo "  -n, --name NAME   Set container name (default: fastapi-ping-service)"
            echo "  -t, --tag TAG     Set image tag (default: latest)"
            echo "  -d, --detach      Run container in background"
            echo "  --rm              Remove container when it exits"
            echo "  -h, --help        Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Check if image exists
if ! docker images "${IMAGE_NAME}:${IMAGE_TAG}" --format "{{.Repository}}" | grep -q "${IMAGE_NAME}"; then
    print_error "Docker image not found: ${IMAGE_NAME}:${IMAGE_TAG}"
    print_status "Please build the image first using: ./build.sh"
    exit 1
fi

# Stop and remove existing container if it exists
if docker ps -a --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    print_warning "Container ${CONTAINER_NAME} already exists. Stopping and removing..."
    docker stop "${CONTAINER_NAME}" &> /dev/null || true
    docker rm "${CONTAINER_NAME}" &> /dev/null || true
fi

# Run the container
print_status "Starting container: ${CONTAINER_NAME}"
print_status "Image: ${IMAGE_NAME}:${IMAGE_TAG}"
print_status "Port mapping: ${HOST_PORT}:${CONTAINER_PORT}"

RUN_CMD="docker run ${DETACH} ${REMOVE} --name ${CONTAINER_NAME} -p ${HOST_PORT}:${CONTAINER_PORT} ${IMAGE_NAME}:${IMAGE_TAG}"

print_status "Executing: $RUN_CMD"

if $RUN_CMD; then
    print_success "Container started successfully!"
    print_status "Application is available at: http://localhost:${HOST_PORT}"
    print_status "Health check endpoint: http://localhost:${HOST_PORT}/ping"
    print_status "API documentation: http://localhost:${HOST_PORT}/docs"
    
    if [[ -z "$DETACH" ]]; then
        print_status "Container is running in foreground. Press Ctrl+C to stop."
    else
        print_status "Container is running in background."
        print_status "To view logs: docker logs ${CONTAINER_NAME}"
        print_status "To stop container: docker stop ${CONTAINER_NAME}"
    fi
else
    print_error "Failed to start container"
    exit 1
fi
