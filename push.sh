#!/bin/bash

# FastAPI Ping Service - Docker Push Script
# This script pushes the Docker image to a private registry

set -e  # Exit on any error

# Configuration
REGISTRY="bznreg.bznins.com"
NAMESPACE="bzn"
IMAGE_NAME="fastapi-ping-service"
IMAGE_TAG="latest"
LOCAL_IMAGE="${IMAGE_NAME}:${IMAGE_TAG}"
REMOTE_IMAGE="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            IMAGE_TAG="$2"
            LOCAL_IMAGE="${IMAGE_NAME}:${IMAGE_TAG}"
            REMOTE_IMAGE="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            LOCAL_IMAGE="${IMAGE_NAME}:${IMAGE_TAG}"
            REMOTE_IMAGE="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            REMOTE_IMAGE="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            REMOTE_IMAGE="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
            shift 2
            ;;
        --build)
            BUILD_FIRST="true"
            shift
            ;;
        --login)
            LOGIN_FIRST="true"
            shift
            ;;
        --insecure)
            INSECURE="true"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -t, --tag TAG         Set image tag (default: latest)"
            echo "  -n, --name NAME       Set image name (default: fastapi-ping-service)"
            echo "  -r, --registry REG    Set registry URL (default: bznreg.bznins.com)"
            echo "  --namespace NS        Set namespace (default: bzn)"
            echo "  --build               Build image before pushing"
            echo "  --login               Login to registry before pushing"
            echo "  --insecure            Skip TLS certificate verification"
            echo "  -h, --help            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Push latest tag"
            echo "  $0 --tag v1.0.0                     # Push specific tag"
            echo "  $0 --build --tag v1.0.0             # Build and push"
            echo "  $0 --login --build --tag v1.0.0     # Login, build and push"
            echo "  $0 --insecure --tag v1.0.0          # Push ignoring SSL errors"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "🚀 Docker Push Script for FastAPI Ping Service"
print_status "Local image:  $LOCAL_IMAGE"
print_status "Remote image: $REMOTE_IMAGE"
echo ""

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Configure Docker for insecure registry if needed
if [[ "$INSECURE" == "true" ]]; then
    print_warning "Using insecure registry (ignoring SSL certificate errors)"
    print_status "Adding $REGISTRY to Docker daemon insecure registries..."

    # Check if Docker daemon config exists
    DOCKER_CONFIG_DIR="/etc/docker"
    DOCKER_CONFIG_FILE="$DOCKER_CONFIG_DIR/daemon.json"

    if [[ -f "$DOCKER_CONFIG_FILE" ]]; then
        print_status "Docker daemon config found at $DOCKER_CONFIG_FILE"
        print_warning "You may need to manually add '$REGISTRY' to insecure-registries"
    else
        print_status "No Docker daemon config found"
        print_warning "You may need to configure Docker daemon to allow insecure registry"
    fi

    print_status "For this session, using --insecure-registry flag where possible"
fi

# Login to registry if requested
if [[ "$LOGIN_FIRST" == "true" ]]; then
    print_status "Logging in to registry: $REGISTRY"

    # Use insecure login if specified
    if [[ "$INSECURE" == "true" ]]; then
        print_warning "Attempting insecure login (ignoring certificate errors)"
        # For insecure registries, we might need to use HTTP instead of HTTPS
        if docker login "$REGISTRY" 2>/dev/null || docker login "http://$REGISTRY" 2>/dev/null; then
            print_success "Successfully logged in to $REGISTRY"
        else
            print_error "Failed to login to $REGISTRY"
            print_error "Try manually: docker login $REGISTRY"
            exit 1
        fi
    else
        if docker login "$REGISTRY"; then
            print_success "Successfully logged in to $REGISTRY"
        else
            print_error "Failed to login to $REGISTRY"
            print_error "If you have SSL certificate issues, try: $0 --insecure"
            exit 1
        fi
    fi
fi

# Build image if requested
if [[ "$BUILD_FIRST" == "true" ]]; then
    print_status "Building image first..."
    if ./build.sh --tag "$IMAGE_TAG" --name "$IMAGE_NAME"; then
        print_success "Image built successfully"
    else
        print_error "Failed to build image"
        exit 1
    fi
fi

# Check if local image exists
if ! docker images "$LOCAL_IMAGE" --format "{{.Repository}}" | grep -q "$IMAGE_NAME"; then
    print_error "Local image not found: $LOCAL_IMAGE"
    print_status "Available images:"
    docker images "$IMAGE_NAME" || echo "No images found for $IMAGE_NAME"
    print_status "Build the image first with: ./build.sh --tag $IMAGE_TAG"
    print_status "Or use: $0 --build --tag $IMAGE_TAG"
    exit 1
fi

# Tag image for remote registry
print_status "Tagging image for remote registry..."
if docker tag "$LOCAL_IMAGE" "$REMOTE_IMAGE"; then
    print_success "Image tagged: $LOCAL_IMAGE -> $REMOTE_IMAGE"
else
    print_error "Failed to tag image"
    exit 1
fi

# Push image to registry
print_status "Pushing image to registry..."
print_status "This may take a while depending on image size and network speed..."

# Handle insecure push
if [[ "$INSECURE" == "true" ]]; then
    print_warning "Attempting insecure push (ignoring certificate errors)"
    # Try different approaches for insecure registries
    if docker push "$REMOTE_IMAGE" 2>/dev/null; then
        PUSH_SUCCESS="true"
    else
        print_warning "HTTPS push failed, this is expected for insecure registries"
        print_status "Please ensure Docker daemon is configured for insecure registry"
        PUSH_SUCCESS="false"
    fi
else
    if docker push "$REMOTE_IMAGE"; then
        PUSH_SUCCESS="true"
    else
        PUSH_SUCCESS="false"
    fi
fi

if [[ "$PUSH_SUCCESS" == "true" ]]; then
    print_success "🎉 Image pushed successfully!"
    print_status "Image URL: $REMOTE_IMAGE"
    
    # Show image info
    print_status "Image information:"
    docker images "$REMOTE_IMAGE" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}" 2>/dev/null || \
    docker images "$LOCAL_IMAGE" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
    
    echo ""
    print_status "To pull this image on another machine:"
    print_status "  docker pull $REMOTE_IMAGE"
    print_status ""
    print_status "To run this image:"
    print_status "  docker run -d -p 8000:8000 --name fastapi-service $REMOTE_IMAGE"
    
else
    print_error "Failed to push image to registry"
    print_error "Common issues:"
    print_error "  1. Not logged in to registry - try: $0 --login"
    print_error "  2. SSL certificate issues - try: $0 --insecure"
    print_error "  3. No permission to push to namespace '$NAMESPACE'"
    print_error "  4. Registry is unreachable"
    print_error "  5. Network connectivity issues"
    print_error ""
    print_error "For SSL certificate issues, you can also:"
    print_error "  - Configure Docker daemon with insecure-registries"
    print_error "  - Add the registry's certificate to system trust store"
    print_error "  - Use: $0 --insecure --login --tag $IMAGE_TAG"
    exit 1
fi
